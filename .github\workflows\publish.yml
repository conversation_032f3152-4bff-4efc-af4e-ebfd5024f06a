name: Publish
on:
    push:
        branches:
            - main

jobs:
    publish:
        runs-on: ubuntu-latest

        permissions:
            contents: read
            id-token: write

        steps:
            - uses: actions/checkout@v4

            - name: Setup Deno
              uses: denoland/setup-deno@v1
              with:
                  deno-version: vx.x.x

            - name: Publish package
              run: deno publish
