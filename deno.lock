{"version": "4", "specifiers": {"jsr:@luca/esbuild-deno-loader@0.11.0": "0.11.0", "jsr:@std/assert@*": "1.0.7", "jsr:@std/assert@1": "1.0.7", "jsr:@std/bytes@^1.0.2": "1.0.3", "jsr:@std/encoding@^1.0.5": "1.0.5", "jsr:@std/internal@^1.0.5": "1.0.5", "jsr:@std/path@^1.0.6": "1.0.8", "npm:bson@*": "6.10.3", "npm:cbor2@*": "1.12.0", "npm:core-js@^3.41.0": "3.41.0", "npm:msgpackr@*": "1.11.2", "npm:superjson@*": "2.2.2"}, "jsr": {"@luca/esbuild-deno-loader@0.11.0": {"integrity": "c05a989aa7c4ee6992a27be5f15cfc5be12834cab7ff84cabb47313737c51a2c", "dependencies": ["jsr:@std/bytes", "jsr:@std/encoding", "jsr:@std/path"]}, "@std/assert@1.0.7": {"integrity": "64ce9fac879e0b9f3042a89b3c3f8ccfc9c984391af19e2087513a79d73e28c3", "dependencies": ["jsr:@std/internal"]}, "@std/bytes@1.0.3": {"integrity": "e5d5b9e685966314e4edb4be60dfc4bd7624a075bfd4ec8109252b4320f76452"}, "@std/encoding@1.0.5": {"integrity": "ecf363d4fc25bd85bd915ff6733a7e79b67e0e7806334af15f4645c569fefc04"}, "@std/internal@1.0.5": {"integrity": "54a546004f769c1ac9e025abd15a76b6671ddc9687e2313b67376125650dc7ba"}, "@std/path@1.0.8": {"integrity": "548fa456bb6a04d3c1a1e7477986b6cffbce95102d0bb447c67c4ee70e0364be"}}, "npm": {"@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3": {"integrity": "sha512-QZHtlVgbAdy2zAqNA9Gu1UpIuI8Xvsd1v8ic6B2pZmeFnFcMWiPLfWXh7TVw4eGEZ/C9TH281KwhVoeQUKbyjw=="}, "@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3": {"integrity": "sha512-mdzd3AVzYKuUmiWOQ8GNhl64/IoFGol569zNRdkLReh6LRLHOXxU4U8eq0JwaD8iFHdVGqSy4IjFL4reoWCDFw=="}, "@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3": {"integrity": "sha512-YxQL+ax0XqBJDZiKimS2XQaf+2wDGVa1enVRGzEvLLVFeqa5kx2bWbtcSXgsxjQB7nRqqIGFIcLteF/sHeVtQg=="}, "@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3": {"integrity": "sha512-fg0uy/dG/nZEXfYilKoRe7yALaNmHoYeIoJuJ7KJ+YyU2bvY8vPv27f7UKhGRpY6euFYqEVhxCFZgAUNQBM3nw=="}, "@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3": {"integrity": "sha512-cvwNfbP07pKUfq1uH+S6KJ7dT9K8WOE4ZiAcsrSes+UY55E/0jLYc+vq+DO7jlmqRb5zAggExKm0H7O/CBaesg=="}, "@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3": {"integrity": "sha512-x0fWaQtYp4E6sktbsdAqnehxDgEc/VwM7uLsRCYWaiGu0ykYdZPiS8zCWdnjHwyiumousxfBm4SO31eXqwEZhQ=="}, "bson@6.10.3": {"integrity": "sha512-MTxGsqgYTwfshYWTRdmZRC+M7FnG1b4y7RO7p2k3X24Wq0yv1m77Wsj0BzlPzd/IowgESfsruQCUToa7vbOpPQ=="}, "cbor2@1.12.0": {"integrity": "sha512-**************************/TBY/JVnDe+mj06NkBjW/ZYOtekaEU4wZ4xcRMNrFkDv8KNtOAqHyDfz3lYg=="}, "copy-anything@3.0.5": {"integrity": "sha512-yCEafptTtb4bk7GLEQoM8KVJpxAfdBJYaXyzQEgQQQgYrZiDp8SJmGKlYza6CYjEDNstAdNdKA3UuoULlEbS6w==", "dependencies": ["is-what"]}, "core-js@3.41.0": {"integrity": "sha512-SJ4/EHwS36QMJd6h/Rg+GyR4A5xE0FSI3eZ+iBVpfqf1x0eTSg1smWLHrA+2jQThZSh97fmSgFSU8B61nxosxA=="}, "detect-libc@2.0.3": {"integrity": "sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw=="}, "is-what@4.1.16": {"integrity": "sha512-ZhMwEosbFJkA0YhFnNDgTM4ZxDRsS6HqTo7qsZM08fehyRYIYa0yHu5R6mgo1n/8MgaPBXiPimPD77baVFYg+A=="}, "msgpackr-extract@3.0.3": {"integrity": "sha512-P0efT1C9jIdVRefqjzOQ9Xml57zpOXnIuS+csaB4MdZbTdmGDLo8XhzBG1N7aO11gKDDkJvBLULeFTo46wwreA==", "dependencies": ["@msgpackr-extract/msgpackr-extract-darwin-arm64", "@msgpackr-extract/msgpackr-extract-darwin-x64", "@msgpackr-extract/msgpackr-extract-linux-arm", "@msgpackr-extract/msgpackr-extract-linux-arm64", "@msgpackr-extract/msgpackr-extract-linux-x64", "@msgpackr-extract/msgpackr-extract-win32-x64", "node-gyp-build-optional-packages"]}, "msgpackr@1.11.2": {"integrity": "sha512-F9UngXRlPyWCDEASDpTf6c9uNhGPTqnTeLVt7bN+bU1eajoR/8V9ys2BRaV5C/e5ihE6sJ9uPIKaYt6bFuO32g==", "dependencies": ["msgpackr-extract"]}, "node-gyp-build-optional-packages@5.2.2": {"integrity": "sha512-s+w+rBWnpTMwSFbaE0UXsRlg7hU4FjekKU4eyAih5T8nJuNZT1nNsskXpxmeqSK9UzkBl6UgRlnKc8hz8IEqOw==", "dependencies": ["detect-libc"]}, "superjson@2.2.2": {"integrity": "sha512-5JRxVqC8I8NuOUjzBbvVJAKNM8qoVuH0O77h4WInc/qC2q5IreqKxYwgkga3PfA22OayK2ikceb/B26dztPl+Q==", "dependencies": ["copy-anything"]}}, "remote": {"https://deno.land/x/denoflate@1.2.1/mod.ts": "f5628e44b80b3d80ed525afa2ba0f12408e3849db817d47a883b801f9ce69dd6", "https://deno.land/x/denoflate@1.2.1/pkg/denoflate.js": "b9f9ad9457d3f12f28b1fb35c555f57443427f74decb403113d67364e4f2caf4", "https://deno.land/x/denoflate@1.2.1/pkg/denoflate_bg.wasm.js": "d581956245407a2115a3d7e8d85a9641c032940a8e810acbd59ca86afd34d44d", "https://deno.land/x/esbuild@v0.24.0/mod.js": "15b51f08198c373555700a695b6c6630a86f2c254938e81be7711eb6d4edc74e"}, "workspace": {"dependencies": ["jsr:@std/assert@1", "npm:core-js@^3.41.0"]}}